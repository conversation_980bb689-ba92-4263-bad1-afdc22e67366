import { ResourceCallbacks } from 'react-admin';

import { fileUploadManager } from '~/components/organisms/FileUpload/core/FileUploadManager';
import { RESOURCES, resourcesInfo } from '../resources';

/**
 * Custom file upload lifecycle handler for hospitality catalogs
 * 
 * This handler is different from the generic one because:
 * - It handles file uploads/updates normally (moving temp files, deleting removed files)
 * - It DOES NOT delete files when catalogs are deleted
 * - This preserves item library integrity since items may exist in multiple catalogs
 */
export const catalogFileUploadHandler: ResourceCallbacks = {
  resource: RESOURCES.HOSPITALITY_CATALOGS,

  beforeCreate: async (params, dataProvider) => {
    console.log(
      'Processing file uploads for hospitality-catalogs beforeCreate'
    );

    const fileFields = ['*.images']; // Find any object with images field (recursive)
    const processedData = await fileUploadManager.processBeforeCreate(
      params.data,
      fileFields
    );

    return { ...params, data: processedData };
  },

  beforeUpdate: async (params, dataProvider) => {
    console.log(
      'Processing file uploads for hospitality-catalogs beforeUpdate'
    );

    const fileFields = ['*.images']; // Find any object with images field (recursive)

    // Get previous data for comparison
    const previousRecord =
      params.previousData ??
      (await dataProvider
        .getOne(RESOURCES.HOSPITALITY_CATALOGS, {
          id: params.id,
          meta: params.meta,
        })
        .then(res => res.data));

    const processedData = await fileUploadManager.processBeforeUpdate(
      params.data,
      previousRecord,
      fileFields
    );

    return { ...params, data: processedData };
  },

  // Intentionally NOT implementing beforeDelete and beforeDeleteMany
  // This prevents file deletion when catalogs are deleted, preserving
  // item library integrity since items may exist in multiple catalogs
};
