import { ResourceCallbacks } from 'react-admin';

import { fileUploadManager } from '~/components/organisms/FileUpload/core/FileUploadManager';
import { RESOURCES } from '../resources';

/**
 * Recursively extract all items from catalog data structure
 * Handles nested items in display groups
 */
const extractAllItems = (data: any): any[] => {
    const items: any[] = [];

    if (!data?.pages) return items;

    const extractFromArray = (arr: any[]): void => {
        for (const item of arr) {
            if (item?.id) {
                items.push(item);
            }
            // If item has nested items (display group), extract those too
            if (item?.items && Array.isArray(item.items)) {
                extractFromArray(item.items);
            }
        }
    };

    // Extract from all pages
    for (const page of data.pages) {
        if (Array.isArray(page)) {
            extractFromArray(page);
        }
    }

    return items;
};

/**
 * Create a map of item ID to item data for quick lookup
 */
const createItemMap = (items: any[]): Map<string, any> => {
    const map = new Map();
    for (const item of items) {
        if (item?.id) {
            map.set(item.id, item);
        }
    }
    return map;
};

/**
 * Custom file upload lifecycle handler for hospitality catalogs
 *
 * This handler is different from the generic one because:
 * - It handles file uploads/updates normally (moving temp files, deleting removed files)
 * - It DOES NOT delete files when catalogs are deleted
 * - This preserves item library integrity since items may exist in multiple catalogs
 */
export const catalogFileUploadHandler: ResourceCallbacks = {
    resource: RESOURCES.HOSPITALITY_CATALOGS,

    beforeCreate: async (params, _dataProvider) => {
        console.log(
            'Processing file uploads for hospitality-catalogs beforeCreate'
        );

        const fileFields = ['*.images']; // Find any object with images field (recursive)
        const processedData = await fileUploadManager.processBeforeCreate(
            params.data,
            fileFields
        );

        return { ...params, data: processedData };
    },

    beforeUpdate: async (params, dataProvider) => {
        console.log(
            'Processing file uploads for hospitality-catalogs beforeUpdate'
        );

        const fileFields = ['*.images']; // Find any object with images field (recursive)

        // Get previous data for comparison
        const previousRecord =
            params.previousData ??
            (await dataProvider
                .getOne(RESOURCES.HOSPITALITY_CATALOGS, {
                    id: params.id,
                    meta: params.meta,
                })
                .then(res => res.data));

        // Extract all items from previous and current catalog data
        const previousItems = extractAllItems(previousRecord);
        const currentItems = extractAllItems(params.data);

        // Create maps for quick lookup
        const previousItemMap = createItemMap(previousItems);
        const currentItemMap = createItemMap(currentItems);

        // For catalogs, we need selective file deletion:
        // 1. Move temporary files to permanent storage for all items
        // 2. Only delete removed images for items that still exist (were edited)
        // 3. Do NOT delete images for items that were completely removed from catalog

        // First, move temporary files to permanent storage
        const processedData = await fileUploadManager.moveTemporaryFiles(
            params.data,
            fileFields
        );

        // Then, selectively delete removed images only for items that were edited
        for (const [itemId, previousItem] of previousItemMap) {
            const currentItem = currentItemMap.get(itemId);

            if (currentItem && previousItem.images && currentItem.images) {
                // Item exists in both previous and current data - check for removed images
                const previousImages = previousItem.images || [];
                const currentImages = currentItem.images || [];

                // Find images that were removed
                const removedImages = previousImages.filter((prevImg: any) =>
                    !currentImages.some((currImg: any) =>
                        prevImg.f === currImg.f && prevImg.e === currImg.e
                    )
                );

                // Delete the removed images
                for (const removedImage of removedImages) {
                    if (!removedImage.x) { // Only delete permanent files (not temporary)
                        try {
                            await fileUploadManager.deleteFile(removedImage);
                        } catch (error) {
                            console.warn('Failed to delete removed image:', error);
                        }
                    }
                }
            }
            // If currentItem doesn't exist, the item was removed from catalog
            // In this case, we do NOT delete its images (preserve for item library)
        }

        return { ...params, data: processedData };
    },

    // Intentionally NOT implementing beforeDelete and beforeDeleteMany
    // This prevents file deletion when catalogs are deleted, preserving
    // item library integrity since items may exist in multiple catalogs
};
