import { ResourceCallbacks } from 'react-admin';

import { fileUploadManager } from '~/components/organisms/FileUpload/core/FileUploadManager';
import { RESOURCES } from '../resources';

/**
 * Custom file upload lifecycle handler for hospitality catalogs
 *
 * This handler is different from the generic one because:
 * - It handles file uploads/updates normally (moving temp files, deleting removed files)
 * - It DOES NOT delete files when catalogs are deleted
 * - This preserves item library integrity since items may exist in multiple catalogs
 */
export const catalogFileUploadHandler: ResourceCallbacks = {
    resource: RESOURCES.HOSPITALITY_CATALOGS,

    beforeCreate: async (params, _dataProvider) => {
        console.log(
            'Processing file uploads for hospitality-catalogs beforeCreate'
        );

        const fileFields = ['*.images']; // Find any object with images field (recursive)
        const processedData = await fileUploadManager.processBeforeCreate(
            params.data,
            fileFields
        );

        return { ...params, data: processedData };
    },

    beforeUpdate: async (params, _dataProvider) => {
        console.log(
            'Processing file uploads for hospitality-catalogs beforeUpdate'
        );

        const fileFields = ['*.images']; // Find any object with images field (recursive)

        // For catalogs, we only move temporary files to permanent storage
        // We DO NOT delete removed files because:
        // 1. Items may be removed from catalogs but still exist in item library
        // 2. Items may exist in multiple catalogs
        // 3. Item library is the source of truth for file ownership
        const processedData = await fileUploadManager.moveTemporaryFiles(
            params.data,
            fileFields
        );

        return { ...params, data: processedData };
    },

    // Intentionally NOT implementing beforeDelete and beforeDeleteMany
    // This prevents file deletion when catalogs are deleted, preserving
    // item library integrity since items may exist in multiple catalogs
};
