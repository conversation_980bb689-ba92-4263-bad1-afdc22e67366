import { ResourceCallbacks } from 'react-admin';

import { fileUploadManager } from '~/components/organisms/FileUpload/core/FileUploadManager';
import { RESOURCES, resourcesInfo } from '../resources';

/**
 * Create a generic file upload lifecycle handler for a specific resource
 */
const createFileUploadHandler = (resource: string): ResourceCallbacks => {
    const resourceInfo = resourcesInfo[resource as keyof typeof resourcesInfo];
    const fileFields = resourceInfo?.uploadedFileFields || [];

    if (fileFields.length === 0) {
        throw new Error(
            `No uploadedFileFields configured for resource: ${resource}`
        );
    }

    return {
        resource,

        beforeCreate: async (params, dataProvider) => {
            console.log(
                `Processing file uploads for ${resource} beforeCreate:`,
                fileFields
            );

            const processedData = await fileUploadManager.processBeforeCreate(
                params.data,
                fileFields
            );

            return { ...params, data: processedData };
        },

        beforeUpdate: async (params, dataProvider) => {
            console.log(
                `Processing file uploads for ${resource} beforeUpdate:`,
                fileFields
            );

            // Get previous data for comparison
            const previousRecord =
                params.previousData ??
                (await dataProvider
                    .getOne(resource, {
                        id: params.id,
                        meta: params.meta,
                    })
                    .then(res => res.data));

            const processedData = await fileUploadManager.processBeforeUpdate(
                params.data,
                previousRecord,
                fileFields
            );

            return { ...params, data: processedData };
        },

        beforeDelete: async (params, dataProvider) => {
            console.log(
                `Processing file cleanup for ${resource} beforeDelete:`,
                fileFields
            );

            // Get entity data to find files to delete
            const entityRecord =
                params.previousData ??
                (await dataProvider
                    .getOne(resource, {
                        id: params.id,
                        meta: params.meta,
                    })
                    .then(res => res.data));

            // Delete all permanent files
            await fileUploadManager.processBeforeDelete(entityRecord, fileFields);

            return params;
        },

        beforeDeleteMany: async (params, dataProvider) => {
            console.log(
                `Processing file cleanup for ${resource} beforeDeleteMany:`,
                fileFields
            );

            // Get entities data to find files to delete
            const entitiesData = await Promise.all(
                params.ids.map(id =>
                    dataProvider
                        .getOne(resource, { id, meta: params.meta })
                        .then(res => res.data)
                )
            );

            // Delete all files for all entities
            await fileUploadManager.processBeforeDeleteMany(entitiesData, fileFields);

            return params;
        },
    };
};

/**
 * Create file upload handlers for all resources that have uploadedFileFields configured
 * Excludes hospitality-catalogs which has its own custom handler
 */
export const createGenericFileUploadHandlers = (): ResourceCallbacks[] => {
    const handlers: ResourceCallbacks[] = [];

    // Iterate through all resources and create handlers for those with uploadedFileFields
    Object.entries(resourcesInfo).forEach(([resourceKey, resourceInfo]) => {
        if (
            resourceInfo.uploadedFileFields &&
            resourceInfo.uploadedFileFields.length > 0
        ) {
            const resource =
                RESOURCES[
                resourceKey.toUpperCase().replace('-', '_') as keyof typeof RESOURCES
                ];

            // Skip hospitality-catalogs as it has its own custom handler
            if (resource && resource !== RESOURCES.HOSPITALITY_CATALOGS) {
                handlers.push(createFileUploadHandler(resource));
            }
        }
    });

    return handlers;
};
